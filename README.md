# Coroutine Objects & Walrus Operator Assignment

## 🎯 Learning Objectives

This assignment will help you master two important Python concepts:

### 1. Coroutine Objects (async/await)

- **Async Functions**: Creating functions that can be paused and resumed
- **Await Keyword**: Waiting for async operations to complete
- **Concurrent Execution**: Running multiple coroutines simultaneously
- **Error Handling**: Managing exceptions in async contexts
- **Task Management**: Creating and managing async tasks

### 2. Walrus Operator (:=)

- **Assignment Expressions**: Assigning values within expressions
- **Conditional Logic**: Using walrus operator in if statements
- **Loop Control**: Efficient loop condition checking
- **Data Validation**: Streamlined validation patterns
- **Performance Optimization**: Reducing redundant calculations

## 📁 Files

- `coroutine_walrus_assignment.py` - Your assignment file (implement here)
- `coroutine_walrus_solution.py` - Complete solution (reference only)
- `README.md` - This file with instructions

## 🚀 Getting Started

1. **Read the assignment** in `coroutine_walrus_assignment.py`
2. **Implement the TODO functions**:
   - `data_collector()`
   - `data_processor()`
   - `statistics_tracker()`
   - `main_pipeline()`
3. **Use walrus operator** in at least 3 different scenarios
4. **Test your implementation** by running the file

## 💡 Key Concepts to Understand

### Coroutines

```python
async def my_coroutine():
    await asyncio.sleep(1)  # Non-blocking sleep
    return "Hello from coroutine!"

# Running coroutines
result = await my_coroutine()
# or
asyncio.run(my_coroutine())
```

### Walrus Operator

```python
# Traditional way
value = some_calculation()
if value > 10:
    print(value)

# With walrus operator
if (value := some_calculation()) > 10:
    print(value)

# In loops
while (data := get_data()) is not None:
    process(data)
```

## 🎯 Assignment Requirements

### Must Implement:

1. **Async Data Collection**: Simulate collecting data from multiple sources
2. **Concurrent Processing**: Process multiple data items simultaneously
3. **Real-time Statistics**: Track processing metrics
4. **Error Handling**: Graceful error management with retries
5. **Walrus Operator Usage**: At least 3 different use cases

### Walrus Operator Examples:

```python
# 1. Data validation
if (value := data_item.value) > 0:
    process(value)

# 2. Error checking
if (error := check_connection()) is not None:
    handle_error(error)

# 3. Statistics calculation
if (new_total := stats.total + 1) > 0:
    stats.average = (stats.average * stats.total + new_value) / new_total
```

## 🔧 Testing Your Implementation

Run your assignment:

```bash
python coroutine_walrus_assignment.py
```

Expected output should show:

- Data collection progress
- Processing confirmations
- Error handling messages
- Final statistics

## 🏆 Bonus Challenges

1. **Rate Limiter**: Implement a coroutine-based rate limiter using walrus operator
2. **Data Filtering**: Add conditional processing with walrus operator
3. **Priority Queue**: Create a priority system for data processing
4. **Batch Processing**: Implement efficient batch operations

## 📚 Learning Resources

### Coroutines:

- [Python asyncio documentation](https://docs.python.org/3/library/asyncio.html)
- [Real Python: Async IO](https://realpython.com/async-io-python/)
- [Python Concurrency with asyncio](https://realpython.com/python-concurrency/)

### Walrus Operator:

- [PEP 572 - Assignment Expressions](https://www.python.org/dev/peps/pep-0572/)
- [Real Python: Walrus Operator](https://realpython.com/python-walrus-operator/)
- [Python 3.8+ Assignment Expressions](https://docs.python.org/3/whatsnew/3.8.html#assignment-expressions)

## 🎓 Assessment Criteria

Your implementation will be evaluated on:

1. **Correctness** (40%): Does the code work as expected?
2. **Walrus Operator Usage** (25%): Effective use of assignment expressions
3. **Async/Await Implementation** (20%): Proper coroutine usage
4. **Error Handling** (10%): Graceful error management
5. **Code Quality** (5%): Clean, readable code

## 💭 Tips for Success

1. **Start Simple**: Begin with basic coroutine functions
2. **Test Incrementally**: Test each function as you implement it
3. **Use Walrus Wisely**: Don't overuse - it should make code clearer, not more complex
4. **Handle Errors**: Always consider what happens when things go wrong
5. **Think Concurrently**: Design for parallel execution

## 🤝 Need Help?

If you get stuck:

1. Review the solution file for guidance
2. Check the Python documentation
3. Test small pieces of code separately
4. Use print statements for debugging

Good luck! 🚀
