"""
COROUTINE OBJECTS & WALRUS OPERATOR ASSIGNMENT
==============================================

This assignment will test your understanding of:
1. Coroutine objects and async/await syntax
2. The walrus operator (:=) for assignment expressions
3. Combining both concepts in practical scenarios

TASK: Build an Async Data Processing Pipeline
=============================================

You are building a system that processes streaming data from multiple sources.
The system needs to:
- Collect data from different sources asynchronously
- Process data in real-time using coroutines
- Use the walrus operator for efficient data handling
- Handle errors gracefully
- Provide real-time statistics

REQUIREMENTS:
============

1. IMPLEMENT THE FOLLOWING FUNCTIONS:
   - data_collector(): A coroutine that simulates collecting data from a source
   - data_processor(): A coroutine that processes collected data
   - statistics_tracker(): A coroutine that tracks real-time statistics
   - main_pipeline(): Orchestrates the entire pipeline

2. USE THE WALRUS OPERATOR (:=) IN AT LEAST 3 DIFFERENT SCENARIOS:
   - Data validation
   - Statistics calculation
   - Error handling

3. IMPLEMENT ERROR HANDLING:
   - Handle connection errors gracefully
   - Use walrus operator for error checking
   - Implement retry logic

4. ADD REAL-TIME STATISTICS:
   - Track total processed items
   - Calculate average processing time
   - Monitor error rates

STARTER CODE:
============
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class DataItem:
    """Represents a data item with metadata"""

    id: int
    source: str
    value: float
    timestamp: float
    processed: bool = False


@dataclass
class Statistics:
    """Tracks real-time statistics"""

    total_processed: int = 0
    total_errors: int = 0
    average_processing_time: float = 0.0
    source_counts: Dict[str, int] = None

    def __post_init__(self):
        if self.source_counts is None:
            self.source_counts = defaultdict(int)


# TODO: IMPLEMENT THESE FUNCTIONS


async def data_collector(source_name: str, delay: float = 0.1) -> DataItem:
    """
    Simulates collecting data from a source.

    Args:
        source_name: Name of the data source
        delay: Delay between data collection attempts

    Returns:
        DataItem: Collected data item

    TODO: Implement this function to:
    1. Simulate data collection with random delays
    2. Generate random data values
    3. Use walrus operator for error simulation
    4. Handle connection errors gracefully
    """
    await asyncio.sleep(delay)
    try: return DataItem(
        id=random.randint(1, 1000),
        source=source_name,
            value=random.random(),
            timestamp=time.time(),
        )
    except Exception as e:
        raise ConnectionError(f"Failed to collect data from {source_name}") from e


async def data_processor(
    data_item: DataItem, processing_delay: float = 0.05
) -> DataItem:
    """
    Processes a data item asynchronously.

    Args:
        data_item: The data item to process
        processing_delay: Simulated processing time

    Returns:
        DataItem: Processed data item

    TODO: Implement this function to:
    1. Simulate data processing with delays
    2. Use walrus operator for validation
    3. Mark item as processed
    4. Handle processing errors
    """
    print(f"Processing data item {data_item.id} from {data_item.source}")
    await asyncio.sleep(processing_delay)
    try: return DataItem(
        id=data_item.id,
        source=data_item.source,
        value=data_item.value,
        timestamp=time.time(),
        processed=True,
    )
    except Exception as e:
        raise ValueError(f"Failed to process data item {data_item.id}") from e


async def statistics_tracker(
    stats: Statistics, data_item: DataItem, processing_time: float
) -> None:
    """
    Updates real-time statistics.

    Args:
        stats: Statistics object to update
        data_item: Processed data item
        processing_time: Time taken to process the item

    TODO: Implement this function to:
    1. Update total processed count
    2. Calculate running average processing time
    3. Track source-specific counts
    4. Use walrus operator for efficient updates
    """
    print(f"Updating statistics for data item {data_item.id} from {data_item.source}"
    
    )
    try: 
        stats.total_processed += 1
        stats.average_processing_time = (stats.average_processing_time * (stats.total_processed - 1) + processing_time) / stats.total_processed
        stats.source_counts[data_item.source] += 1
        stats.total_errors += 1 if not data_item.processed else 0
    except Exception as e:
        raise ValueError(f"Failed to update statistics for data item {data_item.id}") from e


async def main_pipeline(num_sources: int = 3, max_items: int = 20) -> Statistics:
    """
    Main pipeline that orchestrates data collection and processing.

    Args:
        num_sources: Number of data sources to simulate
        max_items: Maximum number of items to process

    Returns:
        Statistics: Final statistics

    TODO: Implement this function to:
    1. Create multiple data collectors
    2. Process data concurrently
    3. Track statistics in real-time
    4. Use walrus operator for pipeline control
    5. Handle errors gracefully
    6. Implement retry logic
    """
    
    
    
    



# BONUS CHALLENGES:
# =================
# 1. Implement a rate limiter using coroutines and walrus operator
# 2. Add data filtering using walrus operator for conditional processing
# 3. Create a priority queue system for data processing
# 4. Implement data batching with walrus operator for batch size calculation


async def run_demo():
    """Demo function to test your implementation"""
    print("🚀 Starting Coroutine & Walrus Operator Demo")
    print("=" * 50)

    stats = await main_pipeline(num_sources=3, max_items=15)

    print("\n📊 Final Statistics:")
    print(f"Total Processed: {stats.total_processed}")
    print(f"Total Errors: {stats.total_errors}")
    print(f"Average Processing Time: {stats.average_processing_time:.3f}s")
    print(f"Source Distribution: {dict(stats.source_counts)}")


if __name__ == "__main__":
    asyncio.run(run_demo())
