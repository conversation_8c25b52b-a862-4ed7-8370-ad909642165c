"""
SOLUTION: Coroutine Objects & Walrus Operator Assignment
=======================================================

This file contains the complete implementation showing how to use:
1. Coroutine objects with async/await
2. Walrus operator (:=) in various scenarios
3. Error handling and retry logic
4. Real-time statistics tracking
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class DataItem:
    """Represents a data item with metadata"""

    id: int
    source: str
    value: float
    timestamp: float
    processed: bool = False


@dataclass
class Statistics:
    """Tracks real-time statistics"""

    total_processed: int = 0
    total_errors: int = 0
    average_processing_time: float = 0.0
    source_counts: Dict[str, int] = None

    def __post_init__(self):
        if self.source_counts is None:
            self.source_counts = defaultdict(int)


async def data_collector(source_name: str, delay: float = 0.1) -> DataItem:
    """
    Simulates collecting data from a source with error handling.

    Uses walrus operator for:
    1. Error simulation and checking
    2. Connection retry logic
    3. Data validation
    """
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        # Simulate connection delay
        await asyncio.sleep(delay)

        # Use walrus operator for error simulation
        if error_occurred := random.random() < 0.1:  # 10% error rate
            retry_count += 1
            print(
                f"❌ Connection error from {source_name}, retry {retry_count}/{max_retries}"
            )
            continue

        # Generate data with validation using walrus operator
        if (value := random.uniform(0, 100)) > 0:  # Ensure positive value
            # Use walrus operator for timestamp validation
            if (timestamp := time.time()) > 0:
                return DataItem(
                    id=random.randint(1000, 9999),
                    source=source_name,
                    value=value,
                    timestamp=timestamp,
                )

    # If all retries failed, raise an exception
    raise ConnectionError(
        f"Failed to collect data from {source_name} after {max_retries} retries"
    )


async def data_processor(
    data_item: DataItem, processing_delay: float = 0.05
) -> DataItem:
    """
    Processes a data item asynchronously with validation.

    Uses walrus operator for:
    1. Data validation
    2. Processing error handling
    3. Result checking
    """
    start_time = time.time()

    # Simulate processing delay
    await asyncio.sleep(processing_delay)

    # Use walrus operator for data validation
    if is_valid := data_item.value >= 0 and data_item.value <= 100:
        # Use walrus operator for processing result
        if (processed_value := data_item.value * 1.1) > 0:  # Apply 10% increase
            data_item.value = processed_value
            data_item.processed = True
            print(
                f"✅ Processed item {data_item.id} from {data_item.source}: {processed_value:.2f}"
            )
        else:
            raise ValueError(f"Invalid processing result: {processed_value}")
    else:
        raise ValueError(f"Invalid data value: {data_item.value}")

    return data_item


async def statistics_tracker(
    stats: Statistics, data_item: DataItem, processing_time: float
) -> None:
    """
    Updates real-time statistics efficiently using walrus operator.

    Uses walrus operator for:
    1. Efficient counter updates
    2. Running average calculation
    3. Source count tracking
    """
    # Update total processed count
    stats.total_processed += 1

    # Use walrus operator for running average calculation
    if (new_total := stats.total_processed) > 0:
        stats.average_processing_time = (
            stats.average_processing_time * (new_total - 1) + processing_time
        ) / new_total

    # Use walrus operator for source count update
    if (source := data_item.source) in stats.source_counts:
        stats.source_counts[source] += 1
    else:
        stats.source_counts[source] = 1


async def main_pipeline(num_sources: int = 3, max_items: int = 20) -> Statistics:
    """
    Main pipeline that orchestrates data collection and processing.

    Uses walrus operator for:
    1. Pipeline control flow
    2. Error handling
    3. Batch processing logic
    """
    stats = Statistics()
    tasks = []
    processed_count = 0

    # Create data sources
    sources = [f"source_{i}" for i in range(num_sources)]

    print(f"🔄 Starting pipeline with {num_sources} sources, max {max_items} items")

    while processed_count < max_items:
        # Use walrus operator for batch size calculation
        if (batch_size := min(3, max_items - processed_count)) > 0:
            # Create collection tasks for this batch
            collection_tasks = [
                data_collector(source, delay=random.uniform(0.05, 0.15))
                for source in sources[:batch_size]
            ]

            # Collect data concurrently
            try:
                collected_items = await asyncio.gather(
                    *collection_tasks, return_exceptions=True
                )

                # Process collected items
                for item in collected_items:
                    if isinstance(item, Exception):
                        stats.total_errors += 1
                        print(f"❌ Collection error: {item}")
                        continue

                    # Use walrus operator for processing time tracking
                    if (start_time := time.time()) > 0:
                        try:
                            processed_item = await data_processor(item)
                            processing_time = time.time() - start_time

                            # Update statistics
                            await statistics_tracker(
                                stats, processed_item, processing_time
                            )
                            processed_count += 1

                            # Use walrus operator for progress tracking
                            if (progress := processed_count % 5) == 0:
                                print(
                                    f"📈 Progress: {processed_count}/{max_items} items processed"
                                )

                        except Exception as e:
                            stats.total_errors += 1
                            print(f"❌ Processing error: {e}")

            except Exception as e:
                stats.total_errors += 1
                print(f"❌ Batch processing error: {e}")

    print(f"🎉 Pipeline completed! Processed {processed_count} items")
    return stats


# BONUS IMPLEMENTATIONS:


async def rate_limiter(max_rate: int = 10):
    """Rate limiter using coroutines and walrus operator"""
    last_check = time.time()
    request_count = 0

    while True:
        current_time = time.time()

        # Use walrus operator for rate calculation
        if (time_diff := current_time - last_check) >= 1.0:
            request_count = 0
            last_check = current_time

        if (current_rate := request_count) < max_rate:
            request_count += 1
            yield True
        else:
            await asyncio.sleep(1.0 - time_diff)


async def data_filter(data_item: DataItem) -> bool:
    """Data filter using walrus operator for conditional processing"""
    # Use walrus operator for filtering conditions
    return (
        (value := data_item.value) > 50  # High value items
        and (source := data_item.source) in ["source_0", "source_1"]  # Specific sources
        and (timestamp := data_item.timestamp) > time.time() - 3600  # Recent items
    )


async def priority_queue_processor(items: List[DataItem]) -> List[DataItem]:
    """Priority queue system using walrus operator"""
    # Use walrus operator for priority calculation
    sorted_items = sorted(
        items,
        key=lambda x: (priority := x.value * 10 + (1 if x.source == "source_0" else 0)),
        reverse=True,
    )
    return sorted_items


async def batch_processor(
    items: List[DataItem], max_batch_size: int = 5
) -> List[DataItem]:
    """Batch processing with dynamic batch size using walrus operator"""
    processed_batches = []

    for i in range(0, len(items), max_batch_size):
        # Use walrus operator for batch size calculation
        if (batch_size := min(max_batch_size, len(items) - i)) > 0:
            batch = items[i : i + batch_size]

            # Process batch concurrently
            batch_tasks = [data_processor(item) for item in batch]
            processed_batch = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # Filter out errors using walrus operator
            valid_results = [
                item
                for item in processed_batch
                if not (
                    isinstance(item, Exception)
                    or (processed := item).processed is False
                )
            ]

            processed_batches.extend(valid_results)

    return processed_batches


async def run_demo():
    """Demo function to test the implementation"""
    print("🚀 Starting Coroutine & Walrus Operator Demo")
    print("=" * 50)

    stats = await main_pipeline(num_sources=3, max_items=15)

    print("\n📊 Final Statistics:")
    print(f"Total Processed: {stats.total_processed}")
    print(f"Total Errors: {stats.total_errors}")
    print(f"Average Processing Time: {stats.average_processing_time:.3f}s")
    print(f"Source Distribution: {dict(stats.source_counts)}")

    # Demo bonus features
    print("\n🎯 Bonus Features Demo:")

    # Rate limiter demo
    print("Rate Limiter:")
    async for _ in rate_limiter(max_rate=3):
        print("  Rate limited request")
        break

    # Data filter demo
    test_item = DataItem(id=1, source="source_0", value=75.0, timestamp=time.time())
    is_filtered = await data_filter(test_item)
    print(f"Data Filter: Item filtered = {is_filtered}")


if __name__ == "__main__":
    asyncio.run(run_demo())
